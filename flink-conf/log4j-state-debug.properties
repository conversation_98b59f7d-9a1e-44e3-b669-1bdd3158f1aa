# Log4j configuration for debugging CopyOnWriteStateMap race conditions
# Copy this to your Flink conf directory and use with:
# -Dlog4j.configuration=file:conf/log4j-state-debug.properties

# Root logger
log4j.rootLogger=INFO, console, file

# Console appender
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %-60c %x - %m%n

# File appender for general logs
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.file=${log.file}
log4j.appender.file.append=true
log4j.appender.file.maxFileSize=100MB
log4j.appender.file.maxBackupIndex=10
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %-60c %x - %m%n

# State debugging appender - separate file for state operations
log4j.appender.stateDebug=org.apache.log4j.RollingFileAppender
log4j.appender.stateDebug.file=${log.file}.state-debug
log4j.appender.stateDebug.append=true
log4j.appender.stateDebug.maxFileSize=500MB
log4j.appender.stateDebug.maxBackupIndex=20
log4j.appender.stateDebug.layout=org.apache.log4j.PatternLayout
log4j.appender.stateDebug.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1} - %m%n

# Race condition detection appender
log4j.appender.raceDetection=org.apache.log4j.RollingFileAppender
log4j.appender.raceDetection.file=${log.file}.race-detection
log4j.appender.raceDetection.append=true
log4j.appender.raceDetection.maxFileSize=100MB
log4j.appender.raceDetection.maxBackupIndex=10
log4j.appender.raceDetection.layout=org.apache.log4j.PatternLayout
log4j.appender.raceDetection.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5p %c{1} - %m%n

# Enable DEBUG logging for state operations
log4j.logger.StateDebug=DEBUG, stateDebug
log4j.additivity.StateDebug=false

# Enable DEBUG logging for race condition detection
log4j.logger.org.apache.flink.runtime.state.heap.RaceConditionDetector=DEBUG, raceDetection
log4j.additivity.org.apache.flink.runtime.state.heap.RaceConditionDetector=false

# Enable DEBUG logging for state consistency validation
log4j.logger.org.apache.flink.runtime.state.heap.StateConsistencyValidator=DEBUG, stateDebug
log4j.additivity.org.apache.flink.runtime.state.heap.StateConsistencyValidator=false

# Reduce noise from other components
log4j.logger.org.apache.flink.runtime.checkpoint=WARN
log4j.logger.org.apache.flink.runtime.executiongraph=WARN
log4j.logger.org.apache.flink.runtime.taskmanager=WARN
log4j.logger.org.apache.flink.runtime.webmonitor=WARN

# Keep important state backend logs
log4j.logger.org.apache.flink.runtime.state=INFO
log4j.logger.org.apache.flink.runtime.state.heap=DEBUG
