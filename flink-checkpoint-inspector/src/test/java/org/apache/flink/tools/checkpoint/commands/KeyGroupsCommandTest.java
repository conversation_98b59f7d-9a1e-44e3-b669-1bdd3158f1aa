/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import picocli.CommandLine;

import java.io.IOException;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * Tests for KeyGroupsCommand.
 */
class KeyGroupsCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecution() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            KeyGroupsCommand command = new KeyGroupsCommand();

            // Set the checkpoint path
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify output contains expected key group information
            assertOutputContains(output, "=== key group distribution analysis ===");
            assertOutputContains(output, "=== key group distribution details ===");
            assertOutputContains(output, "key groups by operator:");
        }
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            CommandLine commandLine = new CommandLine(new KeyGroupsCommand());

            String output = captureOutput(() -> {
                int exitCode = commandLine.execute(testCheckpointPath);
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "=== key group distribution analysis ===");
        }
    }

    @Test
    void testErrorHandling() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Mock an IOException when loading metadata
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenThrow(new IOException("Failed to load checkpoint"));

            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                    Exception.class, command::call);
            assertThat(exception).isInstanceOf(IOException.class);
            assertThat(exception.getMessage()).contains("Failed to load checkpoint");
        }
    }

    @Test
    void testWithNoOperators() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Create metadata with no operators
            CheckpointMetadata emptyMetadata = new CheckpointMetadata(
                    123L,
                    Collections.emptyList(),
                    Collections.emptyList(),
                    mockMetadata.getCheckpointProperties());

            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(emptyMetadata);

            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "=== key group distribution analysis ===");
            assertOutputContains(output, "=== key group summary ===");
            assertOutputContains(output, "total key groups found: 0");
        }
    }

    @Test
    void testKeyGroupDistributionOutput() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Check that key group distribution information is displayed
            assertOutputContains(output, "=== key group distribution details ===");
            assertOutputContains(output, "key groups by operator:");
            assertOutputContains(output, "ranges:");
        }
    }

    @Test
    void testKeyGroupRangeFormatting() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // The output should contain range information for key groups
            // Since we're using mock data, we expect to see some range formatting
            assertOutputContains(output, "key groups");
        }
    }

    @Test
    void testMissingKeyGroupsDetection() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            // This test verifies that the command can detect missing key groups in ranges
            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // The command should analyze key group coverage
            // With our mock data, we might see missing key groups or complete coverage
            assertOutputContains(output, "=== key group distribution details ===");
        }
    }

    @Test
    void testOperatorKeyGroupMapping() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify that the command shows which operators handle which key groups
            assertOutputContains(output, "key groups by operator:");
            // Should show operator IDs and their key group counts
            assertOutputContains(output, "key groups");
        }
    }

    @Test
    void testSubtaskKeyGroupInfo() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            KeyGroupsCommand command = new KeyGroupsCommand();
            setField(command, "checkpointPath", testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // The command should show information about subtasks and their key groups
            assertOutputContains(output, "=== key group distribution details ===");
        }
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = KeyGroupsCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("key-groups");
        assertThat(annotation.description()).containsExactly("Show key group distribution across operators and subtasks");
    }

    @Test
    void testParameterAnnotations() throws Exception {
        // Verify the checkpoint path parameter is properly annotated
        java.lang.reflect.Field pathField = KeyGroupsCommand.class.getDeclaredField("checkpointPath");
        CommandLine.Parameters annotation = pathField.getAnnotation(CommandLine.Parameters.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.index()).isEqualTo("0");
        assertThat(annotation.description()).containsExactly("The path to the checkpoint.");
    }

    /**
     * Helper method to set private fields using reflection.
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
