/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.TestUtils;
import org.apache.flink.tools.checkpoint.utils.S3Configuration;
import org.apache.flink.tools.checkpoint.utils.StateDataReader;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import org.mockito.MockedStatic;
import picocli.CommandLine;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.mock;

/**
 * Integration tests for command interactions and end-to-end scenarios.
 */
class CommandIntegrationTest {

    @TempDir
    Path tempDir;

    private String testCheckpointPath;
    private org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata mockMetadata;
    private ByteArrayOutputStream outputStream;
    private ByteArrayOutputStream errorStream;
    private PrintStream originalOut;
    private PrintStream originalErr;

    @BeforeEach
    void setUp() throws Exception {
        testCheckpointPath = "/test/checkpoint/path";
        mockMetadata = TestUtils.createMockCheckpointMetadata(123L, 2);
        
        // Set up output capture
        outputStream = new ByteArrayOutputStream();
        errorStream = new ByteArrayOutputStream();
        originalOut = System.out;
        originalErr = System.err;
        System.setOut(new PrintStream(outputStream));
        System.setErr(new PrintStream(errorStream));
    }

    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        System.setOut(originalOut);
        System.setErr(originalErr);
    }

    /**
     * Helper method to get the first operator state from metadata.
     */
    private static org.apache.flink.runtime.checkpoint.OperatorState getFirstOperatorState(
            org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata metadata) {
        return new ArrayList<>(metadata.getOperatorStates()).get(0);
    }

    @Test
    void testMetadataToOperatorsWorkflow() throws Exception {
        // Test a typical workflow: first inspect metadata, then list operators
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedInspector.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        System.out.println("    Mock subtask info");
                        return null;
                    });

            // First, run metadata command
            CommandLine metadataCmd = new CommandLine(new MetadataCommand());
            int metadataResult = metadataCmd.execute(testCheckpointPath);
            assertThat(metadataResult).isEqualTo(0);

            // Then, run operators command
            CommandLine operatorsCmd = new CommandLine(new OperatorsCommand());
            int operatorsResult = operatorsCmd.execute(testCheckpointPath);
            assertThat(operatorsResult).isEqualTo(0);

            String output = outputStream.toString();
            assertThat(output).contains("=== Checkpoint Metadata ===");
            assertThat(output).contains("=== Operators ===");
        }
    }

    @Test
    void testOperatorStateInspectionWorkflow() throws Exception {
        // Test workflow: operators -> keyed-state -> operator-state
        String operatorId = getFirstOperatorState(mockMetadata).getOperatorID().toString();
        
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(any(), anyString()))
                    .thenReturn(getFirstOperatorState(mockMetadata));
            mockedInspector.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        System.out.println("    Mock subtask info");
                        return null;
                    });

            // 1. List operators
            CommandLine operatorsCmd = new CommandLine(new OperatorsCommand());
            int operatorsResult = operatorsCmd.execute(testCheckpointPath);
            assertThat(operatorsResult).isEqualTo(0);

            // 2. Inspect keyed state for first operator
            CommandLine keyedStateCmd = new CommandLine(new KeyedStateCommand());
            int keyedStateResult = keyedStateCmd.execute(
                testCheckpointPath, 
                "--operator-id", operatorId,
                "--subtask", "0"
            );
            assertThat(keyedStateResult).isEqualTo(0);

            // 3. Inspect operator state for first operator
            CommandLine operatorStateCmd = new CommandLine(new OperatorStateCommand());
            int operatorStateResult = operatorStateCmd.execute(
                testCheckpointPath, 
                "--operator-id", operatorId,
                "--subtask", "0"
            );
            assertThat(operatorStateResult).isEqualTo(0);

            String output = outputStream.toString();
            assertThat(output).contains("=== Operators ===");
            assertThat(output).contains("=== Keyed State Inspection ===");
            assertThat(output).contains("=== Operator State Inspection ===");
        }
    }

    @Test
    void testDumpStateWorkflow() throws Exception {
        // Test workflow: metadata -> operators -> dump-state
        String operatorId = getFirstOperatorState(mockMetadata).getOperatorID().toString();
        Path outputFile = tempDir.resolve("state-dump.json");
        
        Map<String, Object> mockStateData = new HashMap<>();
        mockStateData.put("key1", "value1");
        mockStateData.put("key2", 123);
        
        // Create a mock StateDataReader manually since MockedConstruction is not available
        StateDataReader mockReader = mock(StateDataReader.class);
        when(mockReader.readStateData(anyString())).thenReturn(mockStateData);

        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {

            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(any(), anyString()))
                    .thenReturn(getFirstOperatorState(mockMetadata));
            mockedInspector.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        System.out.println("    Mock subtask info");
                        return null;
                    });

            // 1. Check metadata
            CommandLine metadataCmd = new CommandLine(new MetadataCommand());
            int metadataResult = metadataCmd.execute(testCheckpointPath);
            assertThat(metadataResult).isEqualTo(0);

            // 2. List operators to find the one we want
            CommandLine operatorsCmd = new CommandLine(new OperatorsCommand());
            int operatorsResult = operatorsCmd.execute(testCheckpointPath);
            assertThat(operatorsResult).isEqualTo(0);

            // 3. Dump state data
            CommandLine dumpStateCmd = new CommandLine(new DumpStateCommand());
            int dumpResult = dumpStateCmd.execute(
                testCheckpointPath,
                "--operator-id", operatorId,
                "--format", "json",
                "--output", outputFile.toString()
            );
            assertThat(dumpResult).isEqualTo(0);

            String output = outputStream.toString();
            assertThat(output).contains("=== Checkpoint Metadata ===");
            assertThat(output).contains("=== Operators ===");
            assertThat(output).contains("=== Dumping State Data ===");
            assertThat(output).contains("State data written to: " + outputFile.toString());
        }
    }

    @Test
    void testKeyGroupAnalysisWorkflow() throws Exception {
        // Test workflow: metadata -> key-groups -> keyed-state
        String operatorId = getFirstOperatorState(mockMetadata).getOperatorID().toString();
        
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(any(), anyString()))
                    .thenReturn(getFirstOperatorState(mockMetadata));

            // 1. Check overall metadata
            CommandLine metadataCmd = new CommandLine(new MetadataCommand());
            int metadataResult = metadataCmd.execute(testCheckpointPath);
            assertThat(metadataResult).isEqualTo(0);

            // 2. Analyze key group distribution
            CommandLine keyGroupsCmd = new CommandLine(new KeyGroupsCommand());
            int keyGroupsResult = keyGroupsCmd.execute(testCheckpointPath);
            assertThat(keyGroupsResult).isEqualTo(0);

            // 3. Inspect specific keyed state
            CommandLine keyedStateCmd = new CommandLine(new KeyedStateCommand());
            int keyedStateResult = keyedStateCmd.execute(
                testCheckpointPath,
                "--operator-id", operatorId,
                "--subtask", "0",
                "--verbose"
            );
            assertThat(keyedStateResult).isEqualTo(0);

            String output = outputStream.toString();
            assertThat(output).contains("=== Checkpoint Metadata ===");
            assertThat(output).contains("=== Key Group Distribution Analysis ===");
            assertThat(output).contains("=== Keyed State Inspection ===");

        }
    }

    @Test
    void testS3HelpIntegration() throws Exception {
        // Test that S3 help can be accessed independently
        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenAnswer(invocation -> {
                        System.out.println("=== S3 Configuration Help ===");
                        System.out.println("S3 configuration details...");
                        return null;
                    });

            CommandLine s3HelpCmd = new CommandLine(new S3HelpCommand());
            int s3HelpResult = s3HelpCmd.execute();
            assertThat(s3HelpResult).isEqualTo(0);

            String output = outputStream.toString();
            assertThat(output).contains("=== S3 Configuration Help ===");
            assertThat(output).contains("S3 configuration details...");
        }
    }

    @Test
    void testErrorPropagationAcrossCommands() throws Exception {
        // Test that errors are properly handled across different commands
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Mock a failure in loading metadata
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenThrow(new java.io.IOException("Checkpoint not found"));

            // All commands that depend on metadata should fail
            CommandLine metadataCmd = new CommandLine(new MetadataCommand());
            int metadataResult = metadataCmd.execute(testCheckpointPath);
            assertThat(metadataResult).isNotEqualTo(0);

            CommandLine operatorsCmd = new CommandLine(new OperatorsCommand());
            int operatorsResult = operatorsCmd.execute(testCheckpointPath);
            assertThat(operatorsResult).isNotEqualTo(0);

            CommandLine keyGroupsCmd = new CommandLine(new KeyGroupsCommand());
            int keyGroupsResult = keyGroupsCmd.execute(testCheckpointPath);
            assertThat(keyGroupsResult).isNotEqualTo(0);
        }
    }

    @Test
    void testCommandLineArgumentValidation() throws Exception {
        // Test that commands properly validate their arguments
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(any(), anyString()))
                    .thenReturn(null); // Operator not found

            // Test missing required arguments
            CommandLine keyedStateCmd = new CommandLine(new KeyedStateCommand());
            int result = keyedStateCmd.execute(testCheckpointPath); // Missing --operator-id
            assertThat(result).isNotEqualTo(0);

            // Test invalid operator ID
            CommandLine keyedStateCmd2 = new CommandLine(new KeyedStateCommand());
            int result2 = keyedStateCmd2.execute(
                testCheckpointPath,
                "--operator-id", "invalid-operator-id",
                "--subtask", "0"
            );
            assertThat(result2).isEqualTo(1); // Should return 1 for operator not found
        }
    }
}
