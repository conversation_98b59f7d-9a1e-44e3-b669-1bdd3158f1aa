<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="repository.spring.milestone" />
      <option name="name" value="Spring Milestone Repository" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="public central repository" />
      <option name="name" value="central" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="repository.jboss.org" />
      <option name="name" value="repository.jboss.org" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Giaitri_Maven_group" />
      <option name="name" value="Giaitri_Maven_group" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="apache.snapshots" />
      <option name="name" value="Apache Snapshot Repository" />
      <option name="url" value="https://artifacts.vtvlive.vn/repository/Giaitri_Maven_group/" />
    </remote-repository>
  </component>
</project>