<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bd1bfe65-13fd-4ad2-b71e-a101adf524d6" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="2z7JaNkeDDMxEMpIKA5r2OUh5lr" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.All in flink-checkpoint-inspector (1).executor": "Run",
    "JUnit.All in flink-checkpoint-inspector.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHELLCHECK.PATH": "I do mind",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "/Users/<USER>/projects/flink/flink-checkpoint-inspector",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="JUnit.All in flink-checkpoint-inspector">
    <configuration name="All in flink-checkpoint-inspector (1)" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="flink-checkpoint-inspector" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_OBJECT" value="package" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="All in flink-checkpoint-inspector" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="flink-checkpoint-inspector" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_OBJECT" value="package" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.All in flink-checkpoint-inspector" />
        <item itemvalue="JUnit.All in flink-checkpoint-inspector (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bd1bfe65-13fd-4ad2-b71e-a101adf524d6" name="Changes" comment="" />
      <created>1751076240382</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751076240382</updated>
      <workItem from="1751076241641" duration="1327000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>