<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Annotation profile for Flink : Checkpoint Inspector" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <option name="project" value="org.apache.flink/flink-checkpoint-inspector" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/info/picocli/picocli-codegen/4.7.5/picocli-codegen-4.7.5.jar" />
          <entry name="$MAVEN_REPOSITORY$/info/picocli/picocli/4.7.5/picocli-4.7.5.jar" />
        </processorPath>
        <module name="flink-checkpoint-inspector" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="flink-checkpoint-inspector" options="-Aproject=org.apache.flink/flink-checkpoint-inspector" />
    </option>
  </component>
</project>