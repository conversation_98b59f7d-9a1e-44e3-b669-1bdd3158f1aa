/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Utility class to detect race conditions in CopyOnWriteStateMap by monitoring internal state
 * consistency and version mismatches.
 */
public class RaceConditionDetector {

    private static final Logger LOG = LoggerFactory.getLogger(RaceConditionDetector.class);

    private final CopyOnWriteStateMap<?, ?, ?> stateMap;
    private final ScheduledExecutorService scheduler;
    private final ConcurrentHashMap<String, VersionSnapshot> versionHistory =
            new ConcurrentHashMap<>();
    private final AtomicLong inconsistencyCount = new AtomicLong(0);

    // Reflection fields for accessing private members
    private final Field stateMapVersionField;
    private final Field highestRequiredSnapshotVersionField;
    private final Field snapshotVersionsField;
    private final Field primaryTableField;
    private final Field incrementalRehashTableField;

    public RaceConditionDetector(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        this.stateMap = stateMap;
        this.scheduler =
                Executors.newSingleThreadScheduledExecutor(
                        r -> {
                            Thread t = new Thread(r, "RaceConditionDetector");
                            t.setDaemon(true);
                            return t;
                        });

        // Initialize reflection fields
        Class<?> clazz = CopyOnWriteStateMap.class;
        this.stateMapVersionField = clazz.getDeclaredField("stateMapVersion");
        this.highestRequiredSnapshotVersionField =
                clazz.getDeclaredField("highestRequiredSnapshotVersion");
        this.snapshotVersionsField = clazz.getDeclaredField("snapshotVersions");
        this.primaryTableField = clazz.getDeclaredField("primaryTable");
        this.incrementalRehashTableField = clazz.getDeclaredField("incrementalRehashTable");

        stateMapVersionField.setAccessible(true);
        highestRequiredSnapshotVersionField.setAccessible(true);
        snapshotVersionsField.setAccessible(true);
        primaryTableField.setAccessible(true);
        incrementalRehashTableField.setAccessible(true);
    }

    /** Start monitoring for race conditions */
    public void startMonitoring(long intervalMs) {
        scheduler.scheduleAtFixedRate(this::checkConsistency, 0, intervalMs, TimeUnit.MILLISECONDS);
        LOG.info("Started race condition monitoring with interval {}ms", intervalMs);
    }

    /** Stop monitoring */
    public void stopMonitoring() {
        scheduler.shutdown();
        LOG.info(
                "Stopped race condition monitoring. Total inconsistencies detected: {}",
                inconsistencyCount.get());
    }

    /** Check internal consistency of the state map */
    private void checkConsistency() {
        try {
            int currentStateMapVersion = (Integer) stateMapVersionField.get(stateMap);
            int currentHighestRequired =
                    (Integer) highestRequiredSnapshotVersionField.get(stateMap);
            Object snapshotVersions = snapshotVersionsField.get(stateMap);
            Object[] primaryTable = (Object[]) primaryTableField.get(stateMap);
            Object[] rehashTable = (Object[]) incrementalRehashTableField.get(stateMap);

            String timestamp = String.valueOf(System.currentTimeMillis());
            VersionSnapshot snapshot =
                    new VersionSnapshot(
                            currentStateMapVersion,
                            currentHighestRequired,
                            snapshotVersions.toString(),
                            primaryTable.length,
                            rehashTable.length,
                            System.nanoTime());

            VersionSnapshot previous = versionHistory.put(timestamp, snapshot);

            // Check for version inconsistencies
            if (previous != null) {
                checkVersionConsistency(previous, snapshot);
                checkTableConsistency(primaryTable, rehashTable);
            }

            // Clean old history to prevent memory leaks
            if (versionHistory.size() > 1000) {
                versionHistory.clear();
            }

        } catch (Exception e) {
            LOG.error("Error during consistency check", e);
        }
    }

    private void checkVersionConsistency(VersionSnapshot previous, VersionSnapshot current) {
        // Check for version going backwards (should never happen)
        if (current.stateMapVersion < previous.stateMapVersion) {
            LOG.error(
                    "RACE CONDITION DETECTED: stateMapVersion went backwards from {} to {}",
                    previous.stateMapVersion,
                    current.stateMapVersion);
            inconsistencyCount.incrementAndGet();
        }

        // Check for highestRequired going backwards without version change
        if (current.highestRequiredVersion < previous.highestRequiredVersion
                && current.stateMapVersion == previous.stateMapVersion) {
            LOG.warn(
                    "POTENTIAL RACE: highestRequiredVersion decreased without version change: {} -> {}",
                    previous.highestRequiredVersion,
                    current.highestRequiredVersion);
        }

        // Check for large version jumps (might indicate missed operations)
        if (current.stateMapVersion - previous.stateMapVersion > 100) {
            LOG.warn(
                    "LARGE VERSION JUMP: {} -> {} (diff: {})",
                    previous.stateMapVersion,
                    current.stateMapVersion,
                    current.stateMapVersion - previous.stateMapVersion);
        }
    }

    private void checkTableConsistency(Object[] primaryTable, Object[] rehashTable) {
        // Check if both tables are non-empty (should only happen during rehash)
        boolean primaryEmpty = isTableEmpty(primaryTable);
        boolean rehashEmpty = isTableEmpty(rehashTable);

        if (!primaryEmpty && !rehashEmpty) {
            LOG.debug(
                    "Rehashing in progress: primary={}, rehash={}",
                    primaryTable.length,
                    rehashTable.length);
        }
    }

    private boolean isTableEmpty(Object[] table) {
        if (table == null || table.length == 0) return true;
        for (Object entry : table) {
            if (entry != null) return false;
        }
        return true;
    }

    /** Get the current inconsistency count */
    public long getInconsistencyCount() {
        return inconsistencyCount.get();
    }

    /** Snapshot of version information at a point in time */
    private static class VersionSnapshot {
        final int stateMapVersion;
        final int highestRequiredVersion;
        final String snapshotVersions;
        final int primaryTableLength;
        final int rehashTableLength;
        final long timestamp;

        VersionSnapshot(
                int stateMapVersion,
                int highestRequiredVersion,
                String snapshotVersions,
                int primaryTableLength,
                int rehashTableLength,
                long timestamp) {
            this.stateMapVersion = stateMapVersion;
            this.highestRequiredVersion = highestRequiredVersion;
            this.snapshotVersions = snapshotVersions;
            this.primaryTableLength = primaryTableLength;
            this.rehashTableLength = rehashTableLength;
            this.timestamp = timestamp;
        }

        @Override
        public String toString() {
            return String.format(
                    "VersionSnapshot{stateMapVersion=%d, highestRequired=%d, "
                            + "snapshotVersions=%s, primaryLen=%d, rehashLen=%d, timestamp=%d}",
                    stateMapVersion,
                    highestRequiredVersion,
                    snapshotVersions,
                    primaryTableLength,
                    rehashTableLength,
                    timestamp);
        }
    }
}
