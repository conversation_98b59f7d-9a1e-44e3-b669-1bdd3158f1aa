/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.runtime.state.internal.InternalValueState;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * A debugging wrapper for ValueState that tracks state operations and detects potential race
 * conditions in CopyOnWriteStateMap.
 */
public class StateConsistencyValidator<T> implements ValueState<T> {

    private static final Logger LOG = LoggerFactory.getLogger(StateConsistencyValidator.class);

    private final InternalValueState<?, ?, T> wrappedState;
    private final String stateName;
    private final ConcurrentHashMap<String, StateAccessRecord> accessHistory =
            new ConcurrentHashMap<>();
    private final AtomicLong operationCounter = new AtomicLong(0);

    public StateConsistencyValidator(InternalValueState<?, ?, T> wrappedState, String stateName) {
        this.wrappedState = wrappedState;
        this.stateName = stateName;
    }

    @Override
    public T value() throws IOException {
        long opId = operationCounter.incrementAndGet();
        String currentKey = getCurrentKeyString();
        long startTime = System.nanoTime();

        try {
            T result = wrappedState.value();
            long endTime = System.nanoTime();

            StateAccessRecord record =
                    new StateAccessRecord(
                            opId,
                            "GET",
                            currentKey,
                            result,
                            startTime,
                            endTime,
                            Thread.currentThread().getName());

            StateAccessRecord previous = accessHistory.put(currentKey, record);

            // Check for potential race conditions
            if (previous != null && previous.operation.equals("PUT") && result == null) {
                LOG.warn(
                        "POTENTIAL RACE CONDITION: Key '{}' was PUT at {} but GET at {} returned null. "
                                + "PUT thread: {}, GET thread: {}, time diff: {}ns",
                        currentKey,
                        previous.timestamp,
                        record.timestamp,
                        previous.threadName,
                        record.threadName,
                        record.timestamp - previous.timestamp);
            }

            if (LOG.isDebugEnabled()) {
                LOG.debug(
                        "GET[{}]: key={}, result={}, duration={}ns, thread={}",
                        opId,
                        currentKey,
                        result,
                        endTime - startTime,
                        Thread.currentThread().getName());
            }

            return result;

        } catch (Exception e) {
            LOG.error("GET operation failed for key '{}', opId={}", currentKey, opId, e);
            throw e;
        }
    }

    @Override
    public void update(T value) throws IOException {
        long opId = operationCounter.incrementAndGet();
        String currentKey = getCurrentKeyString();
        long startTime = System.nanoTime();

        try {
            wrappedState.update(value);
            long endTime = System.nanoTime();

            StateAccessRecord record =
                    new StateAccessRecord(
                            opId,
                            "PUT",
                            currentKey,
                            value,
                            startTime,
                            endTime,
                            Thread.currentThread().getName());

            accessHistory.put(currentKey, record);

            if (LOG.isDebugEnabled()) {
                LOG.debug(
                        "PUT[{}]: key={}, value={}, duration={}ns, thread={}",
                        opId,
                        currentKey,
                        value,
                        endTime - startTime,
                        Thread.currentThread().getName());
            }

        } catch (Exception e) {
            LOG.error(
                    "PUT operation failed for key '{}', value={}, opId={}",
                    currentKey,
                    value,
                    opId,
                    e);
            throw e;
        }
    }

    @Override
    public void clear() {
        long opId = operationCounter.incrementAndGet();
        String currentKey = getCurrentKeyString();

        try {
            wrappedState.clear();
            accessHistory.remove(currentKey);

            if (LOG.isDebugEnabled()) {
                LOG.debug(
                        "CLEAR[{}]: key={}, thread={}",
                        opId,
                        currentKey,
                        Thread.currentThread().getName());
            }

        } catch (Exception e) {
            LOG.error("CLEAR operation failed for key '{}', opId={}", currentKey, opId, e);
            throw e;
        }
    }

    private String getCurrentKeyString() {
        try {
            Object currentKey = wrappedState.getCurrentKey();
            return currentKey != null ? currentKey.toString() : "null";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /** Get access history for debugging purposes */
    public ConcurrentHashMap<String, StateAccessRecord> getAccessHistory() {
        return accessHistory;
    }

    /** Clear access history to prevent memory leaks */
    public void clearHistory() {
        accessHistory.clear();
    }

    /** Record of a state access operation */
    public static class StateAccessRecord {
        public final long operationId;
        public final String operation;
        public final String key;
        public final Object value;
        public final long timestamp;
        public final long duration;
        public final String threadName;

        public StateAccessRecord(
                long operationId,
                String operation,
                String key,
                Object value,
                long startTime,
                long endTime,
                String threadName) {
            this.operationId = operationId;
            this.operation = operation;
            this.key = key;
            this.value = value;
            this.timestamp = startTime;
            this.duration = endTime - startTime;
            this.threadName = threadName;
        }

        @Override
        public String toString() {
            return String.format(
                    "StateAccessRecord{op=%s, key=%s, value=%s, timestamp=%d, duration=%d, thread=%s}",
                    operation, key, value, timestamp, duration, threadName);
        }
    }
}
