/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Specialized detector for race conditions between rehashing and snapshot operations
 * in CopyOnWriteStateMap that can cause entries to be skipped.
 */
public class RehashSnapshotRaceDetector {
    
    private static final Logger LOG = LoggerFactory.getLogger(RehashSnapshotRaceDetector.class);
    
    private final CopyOnWriteStateMap<?, ?, ?> stateMap;
    private final ScheduledExecutorService scheduler;
    private final AtomicLong raceDetectionCount = new AtomicLong(0);
    private final ConcurrentHashMap<String, RehashSnapshot> rehashHistory = new ConcurrentHashMap<>();
    
    // Reflection fields for accessing private members
    private final Field rehashIndexField;
    private final Field primaryTableField;
    private final Field incrementalRehashTableField;
    private final Field primaryTableSizeField;
    private final Field incrementalRehashTableSizeField;
    
    public RehashSnapshotRaceDetector(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        this.stateMap = stateMap;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "RehashSnapshotRaceDetector");
            t.setDaemon(true);
            return t;
        });
        
        // Initialize reflection fields
        Class<?> clazz = CopyOnWriteStateMap.class;
        this.rehashIndexField = clazz.getDeclaredField("rehashIndex");
        this.primaryTableField = clazz.getDeclaredField("primaryTable");
        this.incrementalRehashTableField = clazz.getDeclaredField("incrementalRehashTable");
        this.primaryTableSizeField = clazz.getDeclaredField("primaryTableSize");
        this.incrementalRehashTableSizeField = clazz.getDeclaredField("incrementalRehashTableSize");
        
        rehashIndexField.setAccessible(true);
        primaryTableField.setAccessible(true);
        incrementalRehashTableField.setAccessible(true);
        primaryTableSizeField.setAccessible(true);
        incrementalRehashTableSizeField.setAccessible(true);
    }
    
    /**
     * Start monitoring for rehash-snapshot race conditions
     */
    public void startMonitoring(long intervalMs) {
        scheduler.scheduleAtFixedRate(this::detectRehashRaces, 0, intervalMs, TimeUnit.MILLISECONDS);
        LOG.info("Started rehash-snapshot race detection with interval {}ms", intervalMs);
    }
    
    /**
     * Stop monitoring
     */
    public void stopMonitoring() {
        scheduler.shutdown();
        LOG.info("Stopped rehash-snapshot race detection. Total races detected: {}", 
            raceDetectionCount.get());
    }
    
    /**
     * Detect potential race conditions during rehashing
     */
    private void detectRehashRaces() {
        try {
            int rehashIndex = (Integer) rehashIndexField.get(stateMap);
            Object[] primaryTable = (Object[]) primaryTableField.get(stateMap);
            Object[] incrementalRehashTable = (Object[]) incrementalRehashTableField.get(stateMap);
            int primaryTableSize = (Integer) primaryTableSizeField.get(stateMap);
            int incrementalRehashTableSize = (Integer) incrementalRehashTableSizeField.get(stateMap);
            
            boolean isRehashing = incrementalRehashTable.length > 0;
            
            if (isRehashing) {
                RehashSnapshot snapshot = new RehashSnapshot(
                    rehashIndex,
                    primaryTable.length,
                    incrementalRehashTable.length,
                    primaryTableSize,
                    incrementalRehashTableSize,
                    System.nanoTime()
                );
                
                String key = String.valueOf(System.currentTimeMillis());
                RehashSnapshot previous = rehashHistory.put(key, snapshot);
                
                if (previous != null) {
                    detectSpecificRaceConditions(previous, snapshot);
                }
                
                // Validate snapshot assumptions
                validateSnapshotAssumptions(snapshot);
                
                // Clean old history
                if (rehashHistory.size() > 100) {
                    rehashHistory.clear();
                }
            }
            
        } catch (Exception e) {
            LOG.error("Error during rehash race detection", e);
        }
    }
    
    private void detectSpecificRaceConditions(RehashSnapshot previous, RehashSnapshot current) {
        // Detect rehash index going backwards (should never happen)
        if (current.rehashIndex < previous.rehashIndex && 
            current.primaryTableLength == previous.primaryTableLength) {
            LOG.error("RACE DETECTED: rehashIndex went backwards from {} to {}", 
                previous.rehashIndex, current.rehashIndex);
            raceDetectionCount.incrementAndGet();
        }
        
        // Detect table size inconsistencies
        int previousTotal = previous.primaryTableSize + previous.incrementalRehashTableSize;
        int currentTotal = current.primaryTableSize + current.incrementalRehashTableSize;
        
        if (Math.abs(currentTotal - previousTotal) > 1000) { // Allow for some normal variation
            LOG.warn("LARGE SIZE CHANGE: total entries changed from {} to {} (diff: {})", 
                previousTotal, currentTotal, currentTotal - previousTotal);
        }
        
        // Detect completion race condition
        if (previous.incrementalRehashTableLength > 0 && current.incrementalRehashTableLength == 0) {
            LOG.debug("REHASH COMPLETION detected: this is a critical race window");
            
            // Check if the completion looks suspicious
            if (current.rehashIndex != 0) {
                LOG.error("RACE DETECTED: rehash completed but rehashIndex={} (should be 0)", 
                    current.rehashIndex);
                raceDetectionCount.incrementAndGet();
            }
        }
    }
    
    private void validateSnapshotAssumptions(RehashSnapshot snapshot) {
        // The snapshot logic assumes entries are only in specific regions
        // Let's validate this assumption
        
        if (snapshot.rehashIndex > snapshot.primaryTableLength) {
            LOG.error("INVALID STATE: rehashIndex ({}) > primaryTableLength ({})", 
                snapshot.rehashIndex, snapshot.primaryTableLength);
            raceDetectionCount.incrementAndGet();
        }
        
        // Check if the snapshot regions would cover all possible hash positions
        int expectedRegions = snapshot.rehashIndex + // [0, rehashIndex) in incremental table
                             (snapshot.primaryTableLength - snapshot.rehashIndex) + // [rehashIndex, end) in primary
                             snapshot.rehashIndex; // [length/2, length/2 + rehashIndex) in incremental table
        
        if (expectedRegions > snapshot.incrementalRehashTableLength + snapshot.primaryTableLength) {
            LOG.warn("SNAPSHOT COVERAGE ISSUE: expected regions ({}) > total table space ({})", 
                expectedRegions, snapshot.incrementalRehashTableLength + snapshot.primaryTableLength);
        }
    }
    
    /**
     * Simulate a snapshot operation to detect races
     */
    public boolean simulateSnapshotDuringRehash() {
        try {
            int rehashIndex = (Integer) rehashIndexField.get(stateMap);
            Object[] primaryTable = (Object[]) primaryTableField.get(stateMap);
            Object[] incrementalRehashTable = (Object[]) incrementalRehashTableField.get(stateMap);
            
            boolean isRehashing = incrementalRehashTable.length > 0;
            
            if (isRehashing) {
                // Simulate the snapshot logic
                int localRehashIndex = rehashIndex;
                int localCopyLength = primaryTable.length - localRehashIndex;
                
                // Check if the regions we would copy actually contain all entries
                int entriesInPrimaryRemaining = countEntriesInRange(primaryTable, localRehashIndex, primaryTable.length);
                int entriesInRehashRegion1 = countEntriesInRange(incrementalRehashTable, 0, localRehashIndex);
                int entriesInRehashRegion2 = countEntriesInRange(incrementalRehashTable, 
                    incrementalRehashTable.length >>> 1, 
                    (incrementalRehashTable.length >>> 1) + localRehashIndex);
                
                int totalInSnapshot = entriesInPrimaryRemaining + entriesInRehashRegion1 + entriesInRehashRegion2;
                int actualTotal = stateMap.size();
                
                if (totalInSnapshot != actualTotal) {
                    LOG.error("SNAPSHOT RACE DETECTED: snapshot would capture {} entries but map has {} entries", 
                        totalInSnapshot, actualTotal);
                    raceDetectionCount.incrementAndGet();
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.error("Error during snapshot simulation", e);
            return false;
        }
    }
    
    private int countEntriesInRange(Object[] table, int start, int end) {
        int count = 0;
        for (int i = start; i < end && i < table.length; i++) {
            Object entry = table[i];
            while (entry != null) {
                count++;
                try {
                    Field nextField = entry.getClass().getDeclaredField("next");
                    nextField.setAccessible(true);
                    entry = nextField.get(entry);
                } catch (Exception e) {
                    break;
                }
            }
        }
        return count;
    }
    
    public long getRaceDetectionCount() {
        return raceDetectionCount.get();
    }
    
    private static class RehashSnapshot {
        final int rehashIndex;
        final int primaryTableLength;
        final int incrementalRehashTableLength;
        final int primaryTableSize;
        final int incrementalRehashTableSize;
        final long timestamp;
        
        RehashSnapshot(int rehashIndex, int primaryTableLength, int incrementalRehashTableLength,
                      int primaryTableSize, int incrementalRehashTableSize, long timestamp) {
            this.rehashIndex = rehashIndex;
            this.primaryTableLength = primaryTableLength;
            this.incrementalRehashTableLength = incrementalRehashTableLength;
            this.primaryTableSize = primaryTableSize;
            this.incrementalRehashTableSize = incrementalRehashTableSize;
            this.timestamp = timestamp;
        }
    }
}
