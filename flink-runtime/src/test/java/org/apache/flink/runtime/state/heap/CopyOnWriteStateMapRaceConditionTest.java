/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.util.TestLogger;

import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.CyclicBarrier;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

/**
 * Tests to reliably trigger race conditions in CopyOnWriteStateMap between rehashing and snapshot
 * operations that cause entries to be skipped.
 */
public class CopyOnWriteStateMapRaceConditionTest extends TestLogger {

    private static final int INITIAL_CAPACITY = 4; // Small to trigger rehashing quickly
    private static final int NUM_ENTRIES = 100; // Enough to trigger multiple rehash cycles

    /**
     * Test Scenario 1: Entry placed outside snapshot regions during incremental rehash This test
     * triggers the race where entries are placed at positions not covered by the snapshot logic's
     * assumptions.
     */
    @Test
    public void testSnapshotDuringIncrementalRehashSkipsEntries() throws Exception {
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(INITIAL_CAPACITY, StringSerializer.INSTANCE);

        // Fill map to trigger rehashing
        for (int i = 0; i < NUM_ENTRIES; i++) {
            stateMap.put(i, "namespace", "value-" + i);
        }

        final AtomicInteger entriesSkipped = new AtomicInteger(0);
        final AtomicBoolean testFailed = new AtomicBoolean(false);
        final ExecutorService executor = Executors.newFixedThreadPool(2);
        final CyclicBarrier barrier = new CyclicBarrier(2);

        try {
            // Thread 1: Continuously trigger rehashing by adding/removing entries
            Future<?> rehashThread =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await(); // Synchronize start

                                    for (int round = 0; round < 50; round++) {
                                        // Add entries to trigger rehashing
                                        for (int i = NUM_ENTRIES + round * 10;
                                                i < NUM_ENTRIES + (round + 1) * 10;
                                                i++) {
                                            stateMap.put(i, "namespace", "value-" + i);

                                            // Force incremental rehash steps
                                            triggerIncrementalRehash(stateMap);
                                        }

                                        // Remove some entries
                                        for (int i = NUM_ENTRIES + round * 10;
                                                i < NUM_ENTRIES + round * 10 + 5;
                                                i++) {
                                            stateMap.remove(i, "namespace");
                                        }

                                        Thread.sleep(1); // Small delay to allow snapshot thread to
                                        // interleave
                                    }
                                } catch (Exception e) {
                                    testFailed.set(true);
                                    e.printStackTrace();
                                }
                            });

            // Thread 2: Continuously take snapshots during rehashing
            Future<?> snapshotThread =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await(); // Synchronize start

                                    for (int round = 0; round < 100; round++) {
                                        if (isRehashing(stateMap)) {
                                            // Take snapshot during rehashing
                                            int expectedSize = stateMap.size();
                                            CopyOnWriteStateMap.StateMapEntry<
                                                            Integer, String, String>[]
                                                    snapshot = takeSnapshot(stateMap);

                                            int actualSnapshotSize =
                                                    countEntriesInSnapshot(snapshot);

                                            if (actualSnapshotSize != expectedSize) {
                                                int skipped = expectedSize - actualSnapshotSize;
                                                entriesSkipped.addAndGet(skipped);
                                                System.err.println(
                                                        "RACE DETECTED: Expected "
                                                                + expectedSize
                                                                + " entries, snapshot contains "
                                                                + actualSnapshotSize
                                                                + " entries. Skipped: "
                                                                + skipped);
                                            }
                                        }

                                        Thread.sleep(1); // Small delay
                                    }
                                } catch (Exception e) {
                                    testFailed.set(true);
                                    e.printStackTrace();
                                }
                            });

            rehashThread.get();
            snapshotThread.get();

            if (testFailed.get()) {
                fail("Test failed due to exception");
            }

            // The race condition should cause entries to be skipped
            assertTrue(
                    "Expected entries to be skipped due to race condition, but none were detected. "
                            + "Skipped entries: "
                            + entriesSkipped.get(),
                    entriesSkipped.get() > 0);

        } finally {
            executor.shutdown();
        }
    }

    /**
     * Test Scenario 2: Rehash completion race where snapshot sees inconsistent state This test
     * triggers the race during rehash completion when table references are updated.
     */
    @Test
    public void testSnapshotDuringRehashCompletionRace() throws Exception {
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(INITIAL_CAPACITY, StringSerializer.INSTANCE);

        // Fill map to be just under rehash threshold
        for (int i = 0; i < NUM_ENTRIES; i++) {
            stateMap.put(i, "namespace", "value-" + i);
        }

        final AtomicInteger inconsistentSnapshots = new AtomicInteger(0);
        final AtomicBoolean testFailed = new AtomicBoolean(false);
        final ExecutorService executor = Executors.newFixedThreadPool(2);
        final CountDownLatch startLatch = new CountDownLatch(1);

        try {
            // Thread 1: Trigger rehash completion at precise moments
            Future<?> rehashThread =
                    executor.submit(
                            () -> {
                                try {
                                    startLatch.await();

                                    for (int round = 0; round < 100; round++) {
                                        // Add one entry to trigger rehash completion
                                        int key = NUM_ENTRIES + round;
                                        stateMap.put(key, "namespace", "value-" + key);

                                        // Force the rehash to complete by accessing entries
                                        // This will call
                                        // computeHashForOperationAndDoIncrementalRehash
                                        for (int i = 0; i < 10; i++) {
                                            stateMap.get(i, "namespace");
                                        }

                                        // Remove the entry to reset for next round
                                        stateMap.remove(key, "namespace");

                                        Thread.yield(); // Give snapshot thread a chance
                                    }
                                } catch (Exception e) {
                                    testFailed.set(true);
                                    e.printStackTrace();
                                }
                            });

            // Thread 2: Take snapshots precisely during rehash completion
            Future<?> snapshotThread =
                    executor.submit(
                            () -> {
                                try {
                                    startLatch.await();

                                    for (int round = 0; round < 200; round++) {
                                        try {
                                            // Capture state before snapshot
                                            boolean wasRehashing = isRehashing(stateMap);
                                            int expectedSize = stateMap.size();

                                            // Take snapshot
                                            CopyOnWriteStateMap.StateMapEntry<
                                                            Integer, String, String>[]
                                                    snapshot = takeSnapshot(stateMap);

                                            // Capture state after snapshot
                                            boolean isRehashingAfter = isRehashing(stateMap);

                                            int actualSnapshotSize =
                                                    countEntriesInSnapshot(snapshot);

                                            // Detect inconsistency: if we were rehashing before but
                                            // not after,
                                            // we caught the completion race
                                            if (wasRehashing
                                                    && !isRehashingAfter
                                                    && actualSnapshotSize != expectedSize) {
                                                inconsistentSnapshots.incrementAndGet();
                                                System.err.println(
                                                        "REHASH COMPLETION RACE: Was rehashing before snapshot, "
                                                                + "not rehashing after. Expected: "
                                                                + expectedSize
                                                                + ", Got: "
                                                                + actualSnapshotSize);
                                            }

                                        } catch (Exception e) {
                                            // Snapshot might fail during race - this is expected
                                            inconsistentSnapshots.incrementAndGet();
                                        }

                                        Thread.yield();
                                    }
                                } catch (Exception e) {
                                    testFailed.set(true);
                                    e.printStackTrace();
                                }
                            });

            startLatch.countDown(); // Start both threads

            rehashThread.get();
            snapshotThread.get();

            if (testFailed.get()) {
                fail("Test failed due to exception");
            }

            // The race condition should cause inconsistent snapshots
            assertTrue(
                    "Expected inconsistent snapshots due to rehash completion race, but none detected. "
                            + "Inconsistent snapshots: "
                            + inconsistentSnapshots.get(),
                    inconsistentSnapshots.get() > 0);

        } finally {
            executor.shutdown();
        }
    }

    /**
     * Test Scenario 3: Specific hash collision scenario that exposes the snapshot region bug This
     * test creates entries with specific hash values that will be placed outside the expected
     * snapshot regions.
     */
    @Test
    public void testSpecificHashCollisionSnapshotSkip() throws Exception {
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(8, StringSerializer.INSTANCE); // Start with 8 slots

        // Fill map to trigger rehashing to 16 slots
        for (int i = 0; i < 12; i++) {
            stateMap.put(i, "namespace", "value-" + i);
        }

        // Force rehashing to start
        triggerIncrementalRehash(stateMap);

        // Now add entries with specific hash values that will land outside snapshot regions
        // When rehashing from 8 to 16 slots, snapshot expects entries in:
        // - incrementalRehashTable[0, rehashIndex)
        // - incrementalRehashTable[8, 8 + rehashIndex)
        // But entries can actually be placed anywhere based on hash & 15

        final AtomicReference<Exception> caughtException = new AtomicReference<>();
        final ExecutorService executor = Executors.newFixedThreadPool(2);
        final CyclicBarrier barrier = new CyclicBarrier(2);

        try {
            // Thread 1: Add entries with crafted hash values
            Future<?> addThread =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await();

                                    // Add entries that will hash to positions outside expected
                                    // regions
                                    for (int i = 100; i < 120; i++) {
                                        stateMap.put(i, "namespace", "value-" + i);
                                        triggerIncrementalRehash(stateMap);
                                        Thread.yield();
                                    }
                                } catch (Exception e) {
                                    caughtException.set(e);
                                }
                            });

            // Thread 2: Take snapshots and verify entry counts
            Future<?> snapshotThread =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await();

                                    for (int i = 0; i < 50; i++) {
                                        if (isRehashing(stateMap)) {
                                            int expectedSize = stateMap.size();
                                            CopyOnWriteStateMap.StateMapEntry<
                                                            Integer, String, String>[]
                                                    snapshot = takeSnapshot(stateMap);
                                            int actualSize = countEntriesInSnapshot(snapshot);

                                            if (actualSize != expectedSize) {
                                                throw new AssertionError(
                                                        "Snapshot skipped entries: expected "
                                                                + expectedSize
                                                                + ", got "
                                                                + actualSize
                                                                + " (difference: "
                                                                + (expectedSize - actualSize)
                                                                + ")");
                                            }
                                        }
                                        Thread.yield();
                                    }
                                } catch (Exception e) {
                                    caughtException.set(e);
                                }
                            });

            addThread.get();
            snapshotThread.get();

            // We expect this test to fail due to the race condition
            if (caughtException.get() != null) {
                if (caughtException.get() instanceof AssertionError) {
                    // This is the expected failure - the race condition was triggered
                    System.out.println(
                            "SUCCESS: Race condition detected - "
                                    + caughtException.get().getMessage());
                    return; // Test passed by failing as expected
                } else {
                    throw caughtException.get();
                }
            }

            fail(
                    "Expected race condition to be triggered, but test completed without detecting entry skipping");

        } finally {
            executor.shutdown();
        }
    }

    // Helper methods using reflection to access private fields and methods

    private boolean isRehashing(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Field incrementalRehashTableField =
                CopyOnWriteStateMap.class.getDeclaredField("incrementalRehashTable");
        incrementalRehashTableField.setAccessible(true);
        Object[] incrementalRehashTable = (Object[]) incrementalRehashTableField.get(stateMap);
        return incrementalRehashTable.length > 0;
    }

    private void triggerIncrementalRehash(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Method incrementalRehashMethod =
                CopyOnWriteStateMap.class.getDeclaredMethod("incrementalRehash");
        incrementalRehashMethod.setAccessible(true);
        incrementalRehashMethod.invoke(stateMap);
    }

    @SuppressWarnings("unchecked")
    private CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] takeSnapshot(
            CopyOnWriteStateMap<Integer, String, String> stateMap) throws Exception {
        Method snapshotMethod = CopyOnWriteStateMap.class.getDeclaredMethod("snapshotMapArrays");
        snapshotMethod.setAccessible(true);
        return (CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[])
                snapshotMethod.invoke(stateMap);
    }

    private int countEntriesInSnapshot(CopyOnWriteStateMap.StateMapEntry<?, ?, ?>[] snapshot) {
        int count = 0;
        for (CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry : snapshot) {
            while (entry != null) {
                count++;
                entry = getNextEntry(entry);
            }
        }
        return count;
    }

    private CopyOnWriteStateMap.StateMapEntry<?, ?, ?> getNextEntry(
            CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry) {
        try {
            Field nextField = entry.getClass().getDeclaredField("next");
            nextField.setAccessible(true);
            return (CopyOnWriteStateMap.StateMapEntry<?, ?, ?>) nextField.get(entry);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Test that demonstrates the exact mathematical flaw in snapshot region assumptions. This test
     * creates entries with hash values that will definitely be placed outside the snapshot regions,
     * proving the hypothesis.
     */
    @Test
    public void testMathematicalProofOfSnapshotRegionFlaw() throws Exception {
        // Use a small, controlled table size for predictable hash distribution
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(
                        4, StringSerializer.INSTANCE); // 4 -> 8 slots when rehashing

        // Fill to trigger rehashing from 4 to 8 slots
        stateMap.put(1, "ns", "value1");
        stateMap.put(2, "ns", "value2");
        stateMap.put(3, "ns", "value3");
        stateMap.put(4, "ns", "value4"); // This should trigger rehashing

        // Force rehashing to start but not complete
        triggerIncrementalRehash(stateMap);

        // Get current rehash state
        int rehashIndex = getRehashIndex(stateMap);
        System.out.println("Current rehash index: " + rehashIndex);

        // Now add an entry with a hash that will land outside snapshot regions
        // When rehashing from 4 to 8 slots:
        // - Snapshot expects entries in incrementalTable[0, rehashIndex) and [4, 4+rehashIndex)
        // - But entries can actually be at ANY position [0, 8) based on hash & 7

        // Create an entry that will hash to position 7 (outside expected regions)
        // We need to find a key that when hashed will give us position 7
        Integer keyForPosition7 = findKeyForTargetPosition(7, 8);
        if (keyForPosition7 != null) {
            System.out.println(
                    "Adding key " + keyForPosition7 + " which should hash to position 7");

            // Add this entry during rehashing
            stateMap.put(keyForPosition7, "ns", "value-pos7");

            // Take snapshot immediately
            int expectedSize = stateMap.size();
            CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] snapshot =
                    takeSnapshot(stateMap);
            int actualSize = countEntriesInSnapshot(snapshot);

            System.out.println(
                    "Expected entries: " + expectedSize + ", Snapshot captured: " + actualSize);

            if (actualSize < expectedSize) {
                System.out.println(
                        "SUCCESS: Proved that snapshot skips entries! Missing: "
                                + (expectedSize - actualSize));

                // Verify the missing entry is indeed at position 7
                Object[] incrementalTable = getIncrementalRehashTable(stateMap);
                if (incrementalTable.length > 7 && incrementalTable[7] != null) {
                    System.out.println(
                            "Confirmed: Entry exists at position 7 in incremental table but was skipped by snapshot");
                }

                fail(
                        "Snapshot skipped "
                                + (expectedSize - actualSize)
                                + " entries due to incorrect region assumptions");
            }
        }
    }

    /**
     * Deterministic test that forces the exact race condition scenario. This test manually controls
     * the timing to guarantee the race occurs.
     */
    @Test
    public void testDeterministicRaceCondition() throws Exception {
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(4, StringSerializer.INSTANCE);

        // Fill map to just before rehashing threshold
        stateMap.put(1, "ns", "value1");
        stateMap.put(2, "ns", "value2");
        stateMap.put(3, "ns", "value3");

        final AtomicBoolean raceDetected = new AtomicBoolean(false);
        final ExecutorService executor = Executors.newFixedThreadPool(2);
        final CyclicBarrier barrier = new CyclicBarrier(2);

        try {
            // Thread 1: Add entry to trigger rehashing at precise moment
            Future<?> rehashTrigger =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await();

                                    // Add entry to trigger rehashing
                                    stateMap.put(4, "ns", "value4");

                                    // Manually step through incremental rehash to control timing
                                    for (int step = 0; step < 10; step++) {
                                        if (isRehashing(stateMap)) {
                                            triggerIncrementalRehash(stateMap);
                                            Thread.yield(); // Give snapshot thread a chance
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            });

            // Thread 2: Take snapshot at precise moment during rehashing
            Future<?> snapshotTaker =
                    executor.submit(
                            () -> {
                                try {
                                    barrier.await();
                                    Thread.sleep(1); // Let rehashing start

                                    for (int attempt = 0; attempt < 20; attempt++) {
                                        if (isRehashing(stateMap)) {
                                            int expectedSize = stateMap.size();
                                            CopyOnWriteStateMap.StateMapEntry<
                                                            Integer, String, String>[]
                                                    snapshot = takeSnapshot(stateMap);
                                            int actualSize = countEntriesInSnapshot(snapshot);

                                            if (actualSize != expectedSize) {
                                                raceDetected.set(true);
                                                System.err.println(
                                                        "DETERMINISTIC RACE DETECTED: Expected "
                                                                + expectedSize
                                                                + ", got "
                                                                + actualSize
                                                                + " (attempt "
                                                                + attempt
                                                                + ")");
                                                break;
                                            }
                                        }
                                        Thread.yield();
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            });

            rehashTrigger.get();
            snapshotTaker.get();

            assertTrue(
                    "Deterministic race condition should have been detected", raceDetected.get());

        } finally {
            executor.shutdown();
        }
    }

    // Additional helper methods

    private int getRehashIndex(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Field rehashIndexField = CopyOnWriteStateMap.class.getDeclaredField("rehashIndex");
        rehashIndexField.setAccessible(true);
        return (Integer) rehashIndexField.get(stateMap);
    }

    private Object[] getIncrementalRehashTable(CopyOnWriteStateMap<?, ?, ?> stateMap)
            throws Exception {
        Field incrementalRehashTableField =
                CopyOnWriteStateMap.class.getDeclaredField("incrementalRehashTable");
        incrementalRehashTableField.setAccessible(true);
        return (Object[]) incrementalRehashTableField.get(stateMap);
    }

    /**
     * Find a key that will hash to a specific target position in the table. This is used to create
     * entries that will land outside expected snapshot regions.
     */
    private Integer findKeyForTargetPosition(int targetPosition, int tableSize) {
        // Try different keys until we find one that hashes to the target position
        for (int candidate = 1000; candidate < 10000; candidate++) {
            int hash = computeCompositeHash(candidate, "ns");
            int position = hash & (tableSize - 1);
            if (position == targetPosition) {
                return candidate;
            }
        }
        return null;
    }

    /** Compute the same composite hash that CopyOnWriteStateMap uses internally. */
    private int computeCompositeHash(Object key, Object namespace) {
        // This mirrors the compositeHash method in CopyOnWriteStateMap
        int compositeHash = key.hashCode() ^ namespace.hashCode();
        // Apply bit mixing (simplified version of MathUtils.bitMix)
        compositeHash ^= compositeHash >>> 16;
        compositeHash *= 0x85ebca6b;
        compositeHash ^= compositeHash >>> 13;
        compositeHash *= 0xc2b2ae35;
        compositeHash ^= compositeHash >>> 16;
        return compositeHash;
    }
}
