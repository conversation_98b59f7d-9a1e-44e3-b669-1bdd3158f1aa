/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.state.heap;

import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.util.TestLogger;

import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

/**
 * Simple test to prove the rehash-snapshot race condition that causes entries to be skipped.
 * This test is designed to reliably trigger the race without complex debugging infrastructure.
 */
public class SimpleRehashSnapshotRaceTest extends TestLogger {

    /**
     * Test that demonstrates entries being skipped during snapshot due to rehashing race.
     * This test creates a controlled scenario where the race is highly likely to occur.
     */
    @Test
    public void testSnapshotSkipsEntriesDuringRehash() throws Exception {
        // Use small initial capacity to trigger rehashing quickly
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(2, StringSerializer.INSTANCE);

        // Fill map to trigger rehashing
        stateMap.put(1, "ns", "value1");
        stateMap.put(2, "ns", "value2");
        stateMap.put(3, "ns", "value3");
        stateMap.put(4, "ns", "value4");

        final AtomicInteger entriesSkipped = new AtomicInteger(0);
        final AtomicReference<Exception> testException = new AtomicReference<>();
        final ExecutorService executor = Executors.newFixedThreadPool(2);
        final CountDownLatch startLatch = new CountDownLatch(1);

        try {
            // Thread 1: Continuously add entries to trigger rehashing
            executor.submit(
                    () -> {
                        try {
                            startLatch.await();

                            for (int i = 5; i < 100; i++) {
                                stateMap.put(i, "ns", "value" + i);

                                // Force incremental rehash steps
                                if (i % 3 == 0) {
                                    triggerIncrementalRehash(stateMap);
                                }

                                Thread.yield(); // Give snapshot thread a chance
                            }
                        } catch (Exception e) {
                            testException.set(e);
                        }
                    });

            // Thread 2: Take snapshots during rehashing
            executor.submit(
                    () -> {
                        try {
                            startLatch.await();

                            for (int attempt = 0; attempt < 200; attempt++) {
                                if (isRehashing(stateMap)) {
                                    int expectedSize = stateMap.size();
                                    CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[]
                                            snapshot = takeSnapshot(stateMap);
                                    int actualSize = countEntriesInSnapshot(snapshot);

                                    if (actualSize != expectedSize) {
                                        int skipped = expectedSize - actualSize;
                                        entriesSkipped.addAndGet(skipped);
                                        System.err.println(
                                                "RACE DETECTED: Expected "
                                                        + expectedSize
                                                        + " entries, snapshot contains "
                                                        + actualSize
                                                        + " entries. Skipped: "
                                                        + skipped);
                                    }
                                }

                                Thread.yield();
                            }
                        } catch (Exception e) {
                            testException.set(e);
                        }
                    });

            startLatch.countDown(); // Start both threads

            Thread.sleep(5000); // Let threads run for 5 seconds

            if (testException.get() != null) {
                throw testException.get();
            }

            // The race condition should cause entries to be skipped
            if (entriesSkipped.get() > 0) {
                System.out.println(
                        "SUCCESS: Race condition detected! Total entries skipped: "
                                + entriesSkipped.get());
                fail(
                        "Snapshot skipped "
                                + entriesSkipped.get()
                                + " entries due to rehashing race condition. "
                                + "This proves the hypothesis that the snapshot logic has incorrect assumptions about entry distribution.");
            } else {
                System.out.println(
                        "Race condition not detected in this run. This can happen due to timing.");
                // Don't fail the test - race conditions are timing-dependent
            }

        } finally {
            executor.shutdown();
        }
    }

    /**
     * Test that specifically targets the mathematical flaw in snapshot region assumptions. This
     * test creates entries with hash values that will be placed outside the expected snapshot
     * regions.
     */
    @Test
    public void testSnapshotRegionAssumptionFlaw() throws Exception {
        // Use predictable table sizes
        CopyOnWriteStateMap<Integer, String, String> stateMap =
                new CopyOnWriteStateMap<>(4, StringSerializer.INSTANCE); // Will rehash to 8

        // Fill to trigger rehashing
        for (int i = 1; i <= 6; i++) {
            stateMap.put(i, "ns", "value" + i);
        }

        // Ensure we're in rehashing state
        if (!isRehashing(stateMap)) {
            // Force rehashing by adding more entries
            for (int i = 7; i <= 10; i++) {
                stateMap.put(i, "ns", "value" + i);
                if (isRehashing(stateMap)) {
                    break;
                }
            }
        }

        if (isRehashing(stateMap)) {
            System.out.println("Map is in rehashing state - good for testing");

            // Try to add entries with specific hash patterns that might land outside snapshot
            // regions
            for (int candidate = 1000; candidate < 1100; candidate++) {
                stateMap.put(candidate, "ns", "test-value-" + candidate);

                // Take snapshot and check for missing entries
                int expectedSize = stateMap.size();
                CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] snapshot =
                        takeSnapshot(stateMap);
                int actualSize = countEntriesInSnapshot(snapshot);

                if (actualSize != expectedSize) {
                    System.err.println(
                            "SNAPSHOT REGION FLAW DETECTED: Expected "
                                    + expectedSize
                                    + ", got "
                                    + actualSize
                                    + " (missing: "
                                    + (expectedSize - actualSize)
                                    + ")");
                    fail(
                            "Snapshot missed "
                                    + (expectedSize - actualSize)
                                    + " entries, proving that the snapshot region assumptions are incorrect. "
                                    + "Entries can be placed outside the expected regions [0,rehashIndex) and [tableSize/2, tableSize/2+rehashIndex).");
                }
            }

            System.out.println(
                    "Could not trigger the specific region assumption flaw in this run");
        } else {
            System.out.println("Map did not enter rehashing state - skipping test");
        }
    }

    // Helper methods using reflection

    private boolean isRehashing(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Field incrementalRehashTableField =
                CopyOnWriteStateMap.class.getDeclaredField("incrementalRehashTable");
        incrementalRehashTableField.setAccessible(true);
        Object[] incrementalRehashTable = (Object[]) incrementalRehashTableField.get(stateMap);
        return incrementalRehashTable.length > 0;
    }

    private void triggerIncrementalRehash(CopyOnWriteStateMap<?, ?, ?> stateMap) throws Exception {
        Method incrementalRehashMethod =
                CopyOnWriteStateMap.class.getDeclaredMethod("incrementalRehash");
        incrementalRehashMethod.setAccessible(true);
        incrementalRehashMethod.invoke(stateMap);
    }

    @SuppressWarnings("unchecked")
    private CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[] takeSnapshot(
            CopyOnWriteStateMap<Integer, String, String> stateMap) throws Exception {
        Method snapshotMethod = CopyOnWriteStateMap.class.getDeclaredMethod("snapshotMapArrays");
        snapshotMethod.setAccessible(true);
        return (CopyOnWriteStateMap.StateMapEntry<Integer, String, String>[])
                snapshotMethod.invoke(stateMap);
    }

    private int countEntriesInSnapshot(CopyOnWriteStateMap.StateMapEntry<?, ?, ?>[] snapshot) {
        int count = 0;
        for (CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry : snapshot) {
            while (entry != null) {
                count++;
                entry = getNextEntry(entry);
            }
        }
        return count;
    }

    private CopyOnWriteStateMap.StateMapEntry<?, ?, ?> getNextEntry(
            CopyOnWriteStateMap.StateMapEntry<?, ?, ?> entry) {
        try {
            Field nextField = entry.getClass().getDeclaredField("next");
            nextField.setAccessible(true);
            return (CopyOnWriteStateMap.StateMapEntry<?, ?, ?>) nextField.get(entry);
        } catch (Exception e) {
            return null;
        }
    }
}
